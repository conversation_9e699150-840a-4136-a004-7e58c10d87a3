package com.hl.archive.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 添加留言DTO
 */
@ApiModel(description = "添加留言DTO")
@Data
public class MessageAddDTO {
    /**
     * 所属社团ID
     */
    @ApiModelProperty(value = "所属社团ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clubId;

    /**
     * 父留言ID（回复时使用）
     */
    @ApiModelProperty(value = "父留言ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 根留言ID（回复时使用）
     */
    @ApiModelProperty(value = "根留言ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rootId;

    /**
     * 留言内容
     */
    @ApiModelProperty(value = "留言内容", required = true)
    private String content;

    /**
     * 留言作者身份证号
     */
    @ApiModelProperty(value = "留言作者身份证号", required = true)
    private String authorIdCard;

    /**
     * 回复目标用户身份证号
     */
    @ApiModelProperty(value = "回复目标用户身份证号")
    private String replyToIdCard;

    /**
     * 留言类型（1=普通留言，2=置顶留言，3=公告）
     */
    @ApiModelProperty(value = "留言类型")
    private Integer messageType = 1;
}
