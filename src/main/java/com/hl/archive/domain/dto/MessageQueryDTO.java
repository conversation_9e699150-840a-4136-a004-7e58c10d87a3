package com.hl.archive.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 留言查询DTO
 */
@ApiModel(description = "留言查询DTO")
@Data
public class MessageQueryDTO {
    /**
     * 社团ID
     */
    @ApiModelProperty(value = "社团ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clubId;

    /**
     * 父留言ID（查询回复时使用）
     */
    @ApiModelProperty(value = "父留言ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 根留言ID
     */
    @ApiModelProperty(value = "根留言ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rootId;

    /**
     * 作者身份证号
     */
    @ApiModelProperty(value = "作者身份证号")
    private String authorIdCard;

    /**
     * 留言类型
     */
    @ApiModelProperty(value = "留言类型")
    private Integer messageType;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 是否只查询置顶留言
     */
    @ApiModelProperty(value = "是否只查询置顶留言")
    private Boolean onlyPinned;

    /**
     * 关键词搜索
     */
    @ApiModelProperty(value = "关键词搜索")
    private String keyword;

    /**
     * 当前用户身份证号（用于查询点赞状态）
     */
    @ApiModelProperty(value = "当前用户身份证号")
    private String currentUserIdCard;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小")
    private Integer limit = 20;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String orderBy = "created_at";

    /**
     * 排序方向
     */
    @ApiModelProperty(value = "排序方向")
    private String orderDirection = "DESC";

    /**
     * 是否包含回复列表
     */
    @ApiModelProperty(value = "是否包含回复列表")
    private Boolean includeReplies = false;

    /**
     * 回复列表限制数量
     */
    @ApiModelProperty(value = "回复列表限制数量")
    private Integer replyLimit = 3;
}
