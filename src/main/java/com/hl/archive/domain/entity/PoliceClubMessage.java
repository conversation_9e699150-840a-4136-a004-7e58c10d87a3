package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 社团留言表
 */
@ApiModel(description = "社团留言表")
@Data
@TableName(value = "police_club_message", autoResultMap = true)
public class PoliceClubMessage {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 所属社团ID
     */
    @TableField(value = "club_id")
    @ApiModelProperty(value = "所属社团ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clubId;

    /**
     * 父留言ID（为空表示主留言，不为空表示回复）
     */
    @TableField(value = "parent_id")
    @ApiModelProperty(value = "父留言ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 根留言ID（用于快速查找整个讨论串）
     */
    @TableField(value = "root_id")
    @ApiModelProperty(value = "根留言ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rootId;

    /**
     * 留言内容
     */
    @TableField(value = "content")
    @ApiModelProperty(value = "留言内容")
    private String content;

    /**
     * 留言作者身份证号
     */
    @TableField(value = "author_id_card")
    @ApiModelProperty(value = "留言作者身份证号")
    private String authorIdCard;

    /**
     * 回复目标用户身份证号
     */
    @TableField(value = "reply_to_id_card")
    @ApiModelProperty(value = "回复目标用户身份证号")
    private String replyToIdCard;

    /**
     * 留言类型（1=普通留言，2=置顶留言，3=公告）
     */
    @TableField(value = "message_type")
    @ApiModelProperty(value = "留言类型")
    private Integer messageType;

    /**
     * 是否置顶（0=否，1=是）
     */
    @TableField(value = "is_pinned")
    @ApiModelProperty(value = "是否置顶")
    private Integer isPinned;

    /**
     * 点赞数
     */
    @TableField(value = "like_count")
    @ApiModelProperty(value = "点赞数")
    private Integer likeCount;

    /**
     * 回复数
     */
    @TableField(value = "reply_count")
    @ApiModelProperty(value = "回复数")
    private Integer replyCount;

    /**
     * 状态（0=已删除，1=正常，2=待审核）
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    /**
     * 逻辑删除（0=未删除，1=已删除）
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除")
    @TableLogic
    private Integer isDeleted;

    // 关联信息
    /**
     * 留言作者信息
     */
    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "authorIdCard")
    private JSONObject authorInfo;

    /**
     * 回复目标用户信息
     */
    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "replyToIdCard")
    private JSONObject replyToUserInfo;

    /**
     * 社团信息
     */
    @TableField(exist = false)
    private PoliceClubInfo clubInfo;

    /**
     * 回复列表
     */
    @TableField(exist = false)
    private List<PoliceClubMessage> replies;

    /**
     * 当前用户是否已点赞
     */
    @TableField(exist = false)
    private Boolean isLiked;

    /**
     * 父留言信息（用于显示回复的上下文）
     */
    @TableField(exist = false)
    private PoliceClubMessage parentMessage;
}
