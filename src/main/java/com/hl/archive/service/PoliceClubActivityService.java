package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO;
import com.hl.archive.domain.dto.PoliceClubActivityCreateTaskDTO;
import com.hl.archive.domain.dto.PoliceClubActivityEnrollQueryDTO;
import com.hl.archive.domain.dto.PoliceClubActivityQueryDTO;
import com.hl.archive.domain.entity.PoliceClubActivityEnroll;
import com.hl.archive.domain.entity.PoliceClubInfo;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.config.PoliceClubActivityConfig;
import com.hl.archive.mapper.PoliceClubInfoMapper;
import com.hl.common.config.exception.HlErrException;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceClubActivityMapper;
import com.hl.archive.domain.entity.PoliceClubActivity;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PoliceClubActivityService extends ServiceImpl<PoliceClubActivityMapper, PoliceClubActivity> {


    private final PoliceClubInfoMapper policeClubInfoMapper;

    private final TaskApi taskApi;

    @Value("${spring.security.sso.projectToken}")
    private String token;

    private final PoliceClubActivityConfig policeClubActivityConfig;

    private final PoliceClubActivityEnrollService policeClubActivityEnrollService;

    public Page<PoliceClubActivity> pageList(PoliceClubActivityQueryDTO requestDTO) {
        Page<PoliceClubActivity> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), Wrappers.<PoliceClubActivity>lambdaQuery()
                .like(StrUtil.isNotBlank(requestDTO.getLocation()), PoliceClubActivity::getLocation, requestDTO.getLocation())
                .like(StrUtil.isNotBlank(requestDTO.getActivityTime()), PoliceClubActivity::getActivityTime, requestDTO)
                .eq(requestDTO.getClubId() != null, PoliceClubActivity::getClubId, requestDTO.getClubId())
                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PoliceClubActivity::getActivityName, requestDTO.getQuery())
                .and(requestDTO.isHasMedia(), i ->
                        i.isNotNull(PoliceClubActivity::getMedia)
                                .ne(PoliceClubActivity::getMedia, "[]")
                )
                .orderByDesc(PoliceClubActivity::getActivityTime));

        // 获取社团信息
        List<Long> clubIdList = page.getRecords().stream().map(PoliceClubActivity::getClubId).collect(Collectors.toList());
        if (!clubIdList.isEmpty()) {
            List<PoliceClubInfo> clubInfoList = policeClubInfoMapper.selectBatchIds(clubIdList);
            Map<Long, PoliceClubInfo> policeClubInfoMap = clubInfoList.stream()
                    .collect(Collectors.toMap(PoliceClubInfo::getId, c -> c));
            page.getRecords().forEach(r -> r.setClubInfo(policeClubInfoMap.get(r.getClubId())));
        }

        return page;
    }

    public void saveClubActivity(JSONObject contentData) {
        try {
            String customId = contentData.getByPath("data.custom_id").toString();
            if (!"ZZCSP".equals(customId)) return;

            String passStatus = contentData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)) return;

            String taskId = contentData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, param);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            JSONObject content = parsed.getJSONObject("all_content");
            PoliceClubActivity clubActivity = new PoliceClubActivity();
            clubActivity.setClubId(Long.valueOf(content.getString("ssst")));
            clubActivity.setActivityTime(DateUtil.parse(content.getString(policeClubActivityConfig.getActivityTime())).toLocalDateTime());
            clubActivity.setLocation(content.getString(policeClubActivityConfig.getLocation()));
            clubActivity.setParticipants(content.getList(policeClubActivityConfig.getParticipants(), String.class));
            clubActivity.setAdvancePayment(content.getString(policeClubActivityConfig.getAdvancePayment()));
            clubActivity.setActivityName(content.getString(policeClubActivityConfig.getActivityName()));
            clubActivity.setActivityBudget(content.getString(policeClubActivityConfig.getActivityBudget()));
            clubActivity.setCreatedBy(content.getString("id_card"));
            clubActivity.setTaskId(taskId);
            clubActivity.setActivityNotice(content.getList(policeClubActivityConfig.getActivityNotice(), JSONObject.class));
            clubActivity.setActivityPlan(content.getList(policeClubActivityConfig.getActivityPlan(), JSONObject.class));
            clubActivity.setAllowedParticipantCount(content.getInteger(policeClubActivityConfig.getAllowedParticipantCount()));
            clubActivity.setBudgetDetail(content.getString(policeClubActivityConfig.getBudgetDetail()));
            boolean save = this.save(clubActivity);
            log.info("社团活动保存成功:{}", clubActivity);

            checkActivityStatus(clubActivity.getId());
        } catch (NumberFormatException e) {
            log.error("社团活动保存失败");
            log.error(e.getMessage(), e);
        }
    }

    public void updateClubActivityMedia(JSONObject contentData) {
        try {
            String taskId = contentData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, param);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            JSONObject content = parsed.getJSONObject("all_content");

            PoliceClubActivity clubActivity = this.getOne(Wrappers.<PoliceClubActivity>lambdaQuery()
                    .eq(PoliceClubActivity::getTaskId, taskId));
            clubActivity.setMedia(content.getList(policeClubActivityConfig.getMedia(), JSONObject.class));
            clubActivity.setAttachment(content.getList(policeClubActivityConfig.getAttachment(), JSONObject.class));
            this.updateById(clubActivity);
        } catch (Exception e) {
            log.error("更新社团活动附件失败");
            log.error(e.getMessage(), e);
        }
    }

    public void deleteClubActivity(JSONObject contentData) {
        try {
            String taskId = contentData.getByPath("data.task_id").toString();
            this.remove(Wrappers.<PoliceClubActivity>lambdaQuery()
                    .eq(PoliceClubActivity::getTaskId, taskId));
            log.info("社团活动删除成功:{}", taskId);
        } catch (Exception e) {
            log.error("删除社团活动失败");
            log.error(e.getMessage(), e);
        }
    }

    public R<?> createActivityTask(PoliceClubActivityCreateTaskDTO request) {
        JSONObject param = new JSONObject();
        Integer cjrs = request.getCjrs();

        List<String> cjry = request.getCjry();
        if (cjry != null && !cjry.isEmpty()) {
            if (cjry.size() > cjrs) {
                return R.fail("参加人员超过允许参加人数");
            }
        }
        param.put("config_uuid", policeClubActivityConfig.getConfigUuid());
        param.put("ssst", request.getSsst());
        param.put("hdsj", request.getHdsj());
        param.put("hddd", request.getHddd());
        param.put("cjry", request.getCjry());
        param.put("hdmc", request.getHdmc());
        param.put("ywyz", request.getYwyz());
        param.put("hdjf", request.getHdjf());
        param.put("ysmx", request.getYsmx());
        param.put("hdtz", request.getHdtz());
        param.put("hdjh", request.getHdjh());
        param.put("cjrs", request.getCjrs());
        String token = UserUtils.getUser().getToken();
        R<JSONArray> taskRes = taskApi.add(token, param);
        return taskRes;
    }

    public Page<PoliceClubActivityEnroll> pageEnrollList(PoliceClubActivityEnrollQueryDTO requestDTO) {
        Page<PoliceClubActivityEnroll> page = policeClubActivityEnrollService.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), Wrappers.<PoliceClubActivityEnroll>lambdaQuery()
                .eq(PoliceClubActivityEnroll::getActivityId, requestDTO.getActivityId()));
        return page;
    }

    public boolean enrollPermission(PoliceClubActivityEnroll request) {
        Long activityId = request.getActivityId();
        PoliceClubActivity clubActivity = this.getOne(Wrappers.<PoliceClubActivity>lambdaQuery()
                .eq(PoliceClubActivity::getId, activityId));

        if (clubActivity.getActivityStatus() != 0) {
            // 活动状态 非报名 也无权限
            return false;
        }

        if (clubActivity.getAllowedParticipantCount() != null && clubActivity.getParticipants() != null && clubActivity.getParticipants().size() >= clubActivity.getAllowedParticipantCount()) {
            // 活动人数以满也无权限
            return false;
        }


        List<String> participants = clubActivity.getParticipants();

        String idCard = UserUtils.getUser().getIdCard();
        if (participants != null && participants.contains(idCard)) {
            return false;
        }

        PoliceClubActivityEnroll policeClubActivityEnroll = policeClubActivityEnrollService.getOne(Wrappers.<PoliceClubActivityEnroll>lambdaQuery()
                .eq(PoliceClubActivityEnroll::getActivityId, activityId)
                .eq(PoliceClubActivityEnroll::getIdCard, idCard)
                .in(PoliceClubActivityEnroll::getStatus, 0, 2)
                .last("limit 1"));
        return policeClubActivityEnroll == null;
    }

    public Boolean saveEnroll(PoliceClubActivityEnroll request) {


        PoliceClubActivity clubActivity = this.getOne(Wrappers.<PoliceClubActivity>lambdaQuery()
                .eq(PoliceClubActivity::getId, request.getActivityId()));
        if (clubActivity.getActivityStatus() != 0) {
            // 活动状态 非报名 也无权限
            throw new RuntimeException("活动不在报名期间");
        }
        if (clubActivity.getAllowedParticipantCount() != null && clubActivity.getParticipants().size() >= clubActivity.getAllowedParticipantCount()) {
            // 活动人数以满也无权限
            throw new RuntimeException("活动人数已满");
        }

        request.setIdCard(UserUtils.getUser().getIdCard());
        request.setEnrollTime(LocalDateTime.now());
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        boolean save = policeClubActivityEnrollService.save(request);

        return save ;
    }

    @Transactional
    public Boolean approveEnroll(PoliceClubActivityEnroll request) {
        request.setApprovePerson(UserUtils.getUser().getIdCard());
        request.setUpdateAt(LocalDateTime.now());
        boolean b = policeClubActivityEnrollService.updateById(request);
        if (request.getStatus() == 1) {
            // 审批通过
            Long activityId = request.getActivityId();
            boolean checkActivityStatus = checkActivityStatus(activityId);
            if (checkActivityStatus){
                throw new HlErrException("活动人数已满 无法审核通过");
            }
            PoliceClubActivity clubActivity = this.getOne(Wrappers.<PoliceClubActivity>lambdaQuery()
                    .eq(PoliceClubActivity::getId, activityId));
            PoliceClubActivityEnroll enrollServiceById = policeClubActivityEnrollService.getById(request.getId());
            clubActivity.getParticipants().add(enrollServiceById.getIdCard());
            boolean save = this.updateById(clubActivity);
            checkActivityStatus(activityId);
            return save;
        }
        return b;
    }

    public R<?> approveActivityTask(PoliceClubActivityApproveTaskDTO request) {
        String token = UserUtils.getUser().getToken();
        String taskId = request.getTaskId();
        if (request.getPass() == 1) {
            JSONObject taskParam = new JSONObject();
            taskParam.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, taskParam);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            JSONObject allContent = parsed.getJSONObject("all_content");
            List<String> cjry = allContent.getList("cjry", String.class);
            Integer cjrs = request.getCjrs();
            if (cjry.size() > cjrs) {
                return R.fail("参加人员超过允许参加人数 不允许审核通过");
            }
        }


        // 审批任务
        JSONObject param = new JSONObject();
        param.put("task_id", taskId);
//        param.put("node_id", "ZZCSP");
        JSONObject content = new JSONObject();
        content.put("pass", request.getPass());
        content.put("text", request.getText());
        content.put("rwsj", Lists.newArrayList(new JSONObject()
                .fluentPut("cjrs", request.getCjrs())));
        content.put("sign_url", UserUtils.getUser().getPoliceId());
        param.put("content", content);
        log.info("社团活动审批传参: {}", param);
        R<JSONArray> taskRes = taskApi.audit(token, param);
        // 修改参加人数
        if (request.getPass() == 1) {
            PoliceClubActivity clubActivity = this.getOne(Wrappers.<PoliceClubActivity>lambdaQuery()
                    .eq(PoliceClubActivity::getTaskId, taskId)
                    .last(" limit 1"));
            clubActivity.setAllowedParticipantCount(request.getCjrs());
            this.updateById(clubActivity);
        }
        return taskRes;

    }

    public PoliceClubActivity queryDetail(PoliceClubActivity request) {
        PoliceClubActivity clubActivity = this.getOne(Wrappers.<PoliceClubActivity>lambdaQuery()
                .eq(PoliceClubActivity::getId, request.getId()));
        Long clubId = clubActivity.getClubId();
        PoliceClubInfo one = policeClubInfoMapper.selectOne(Wrappers.<PoliceClubInfo>lambdaQuery()
                .eq(PoliceClubInfo::getId, clubId));
        clubActivity.setClubInfo(one);
        return clubActivity;
    }


    public boolean checkActivityStatus(Long activityId) {
        try {
            PoliceClubActivity clubActivity = this.getOne(Wrappers.<PoliceClubActivity>lambdaQuery()
                    .eq(PoliceClubActivity::getId, activityId));
            if (clubActivity != null) {
                Integer allowedParticipantCount = clubActivity.getAllowedParticipantCount();
                List<String> participants = clubActivity.getParticipants();
                if (allowedParticipantCount != null && participants != null && participants.size() >= allowedParticipantCount) {
                    clubActivity.setActivityStatus(1);
                    return this.updateById(clubActivity);
                }
            }
            return false;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
}
