<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceClubMessageMapper">

    <!-- 分页查询留言列表（包含点赞状态） -->
    <select id="selectMessagePage" resultType="com.hl.archive.domain.entity.PoliceClubMessage">
        SELECT 
            m.*,
            CASE WHEN l.id IS NOT NULL THEN 1 ELSE 0 END as isLiked
        FROM police_club_message m
        LEFT JOIN police_club_message_like l ON m.id = l.message_id 
            AND l.user_id_card = #{dto.currentUserIdCard}
        WHERE m.is_deleted = 0
        <if test="dto.clubId != null">
            AND m.club_id = #{dto.clubId}
        </if>
        <if test="dto.parentId != null">
            AND m.parent_id = #{dto.parentId}
        </if>
        <if test="dto.rootId != null">
            AND m.root_id = #{dto.rootId}
        </if>
        <if test="dto.authorIdCard != null and dto.authorIdCard != ''">
            AND m.author_id_card = #{dto.authorIdCard}
        </if>
        <if test="dto.messageType != null">
            AND m.message_type = #{dto.messageType}
        </if>
        <if test="dto.status != null">
            AND m.status = #{dto.status}
        </if>
        <if test="dto.onlyPinned != null and dto.onlyPinned == true">
            AND m.is_pinned = 1
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            AND m.content LIKE CONCAT('%', #{dto.keyword}, '%')
        </if>
        ORDER BY 
            m.is_pinned DESC,
            <choose>
                <when test="dto.orderBy == 'created_at' and dto.orderDirection == 'ASC'">
                    m.created_at ASC
                </when>
                <when test="dto.orderBy == 'like_count' and dto.orderDirection == 'DESC'">
                    m.like_count DESC, m.created_at DESC
                </when>
                <when test="dto.orderBy == 'like_count' and dto.orderDirection == 'ASC'">
                    m.like_count ASC, m.created_at DESC
                </when>
                <when test="dto.orderBy == 'reply_count' and dto.orderDirection == 'DESC'">
                    m.reply_count DESC, m.created_at DESC
                </when>
                <when test="dto.orderBy == 'reply_count' and dto.orderDirection == 'ASC'">
                    m.reply_count ASC, m.created_at DESC
                </when>
                <otherwise>
                    m.created_at DESC
                </otherwise>
            </choose>
    </select>

    <!-- 查询留言的回复列表 -->
    <select id="selectRepliesByParentId" resultType="com.hl.archive.domain.entity.PoliceClubMessage">
        SELECT 
            m.*,
            CASE WHEN l.id IS NOT NULL THEN 1 ELSE 0 END as isLiked
        FROM police_club_message m
        LEFT JOIN police_club_message_like l ON m.id = l.message_id 
            AND l.user_id_card = #{currentUserIdCard}
        WHERE m.parent_id = #{parentId}
            AND m.is_deleted = 0
            AND m.status = 1
        ORDER BY m.created_at ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询用户在指定留言的点赞状态 -->
    <select id="selectUserLikedMessageIds" resultType="java.lang.Long">
        SELECT message_id
        FROM police_club_message_like
        WHERE message_id IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
        AND user_id_card = #{userIdCard}
    </select>

    <!-- 更新留言的点赞数 -->
    <update id="updateLikeCount">
        UPDATE police_club_message 
        SET like_count = like_count + #{increment},
            updated_at = NOW()
        WHERE id = #{messageId}
    </update>

    <!-- 更新留言的回复数 -->
    <update id="updateReplyCount">
        UPDATE police_club_message 
        SET reply_count = reply_count + #{increment},
            updated_at = NOW()
        WHERE id = #{messageId}
    </update>

    <!-- 查询留言详情（包含关联信息） -->
    <select id="selectMessageDetail" resultType="com.hl.archive.domain.entity.PoliceClubMessage">
        SELECT 
            m.*,
            CASE WHEN l.id IS NOT NULL THEN 1 ELSE 0 END as isLiked
        FROM police_club_message m
        LEFT JOIN police_club_message_like l ON m.id = l.message_id 
            AND l.user_id_card = #{currentUserIdCard}
        WHERE m.id = #{messageId}
            AND m.is_deleted = 0
    </select>

    <!-- 批量查询留言的回复数量 -->
    <select id="selectReplyCountByMessageIds" resultType="com.hl.archive.domain.entity.PoliceClubMessage">
        SELECT id, reply_count
        FROM police_club_message
        WHERE id IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
        AND is_deleted = 0
    </select>

</mapper>
