package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjlxx;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:08:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceResumeToVWjRyjlxxMapperImpl implements PoliceResumeToVWjRyjlxxMapper {

    @Override
    public VWjRyjlxx convert(PoliceResume source) {
        if ( source == null ) {
            return null;
        }

        VWjRyjlxx vWjRyjlxx = new VWjRyjlxx();

        if ( source.getEndDate() != null ) {
            vWjRyjlxx.setJzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEndDate() ) );
        }
        vWjRyjlxx.setGmsfhm( source.getIdCard() );
        if ( source.getStartDate() != null ) {
            vWjRyjlxx.setQssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getStartDate() ) );
        }
        vWjRyjlxx.setSzdw( source.getWorkUnit() );
        vWjRyjlxx.setZw( source.getPosition() );

        return vWjRyjlxx;
    }

    @Override
    public VWjRyjlxx convert(PoliceResume source, VWjRyjlxx target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getEndDate() != null ) {
            target.setJzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEndDate() ) );
        }
        else {
            target.setJzsj( null );
        }
        target.setGmsfhm( source.getIdCard() );
        if ( source.getStartDate() != null ) {
            target.setQssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getStartDate() ) );
        }
        else {
            target.setQssj( null );
        }
        target.setSzdw( source.getWorkUnit() );
        target.setZw( source.getPosition() );

        return target;
    }
}
