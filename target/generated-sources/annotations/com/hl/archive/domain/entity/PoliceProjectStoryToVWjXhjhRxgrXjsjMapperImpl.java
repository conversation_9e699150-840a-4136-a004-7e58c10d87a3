package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXhjhRxgrXjsj;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T09:25:34+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceProjectStoryToVWjXhjhRxgrXjsjMapperImpl implements PoliceProjectStoryToVWjXhjhRxgrXjsjMapper {

    @Override
    public VWjXhjhRxgrXjsj convert(PoliceProjectStory source) {
        if ( source == null ) {
            return null;
        }

        VWjXhjhRxgrXjsj vWjXhjhRxgrXjsj = new VWjXhjhRxgrXjsj();

        if ( source.getRegisterTime() != null ) {
            vWjXhjhRxgrXjsj.setDjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRegisterTime() ) );
        }
        vWjXhjhRxgrXjsj.setDjrxm( source.getRegisteredBy() );
        vWjXhjhRxgrXjsj.setJlXxzjbh( source.getXhjhZjbh() );
        vWjXhjhRxgrXjsj.setXjsjBt( source.getTitle() );
        vWjXhjhRxgrXjsj.setXjsjNr( source.getContent() );
        vWjXhjhRxgrXjsj.setXxzjbh( source.getZjbh() );

        return vWjXhjhRxgrXjsj;
    }

    @Override
    public VWjXhjhRxgrXjsj convert(PoliceProjectStory source, VWjXhjhRxgrXjsj target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getRegisterTime() != null ) {
            target.setDjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRegisterTime() ) );
        }
        else {
            target.setDjsj( null );
        }
        target.setDjrxm( source.getRegisteredBy() );
        target.setJlXxzjbh( source.getXhjhZjbh() );
        target.setXjsjBt( source.getTitle() );
        target.setXjsjNr( source.getContent() );
        target.setXxzjbh( source.getZjbh() );

        return target;
    }
}
