package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXhjhRxgrTwzl;
import com.hl.orasync.domain.VWjXhjhRxgrTwzlToPoliceProjectMaterialMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {ConversionUtils.class,VWjXhjhRxgrTwzlToPoliceProjectMaterialMapper.class},
    imports = {}
)
public interface PoliceProjectMaterialToVWjXhjhRxgrTwzlMapper extends BaseMapper<PoliceProjectMaterial, VWjXhjhRxgrTwzl> {
  @Mapping(
      target = "jlXxzjbh",
      source = "xhjhZjbh"
  )
  @Mapping(
      target = "tpzlBt",
      source = "imageName"
  )
  @Mapping(
      target = "tpzlNr",
      source = "imageUrl"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjXhjhRxgrTwzl convert(PoliceProjectMaterial source);

  @Mapping(
      target = "jlXxzjbh",
      source = "xhjhZjbh"
  )
  @Mapping(
      target = "tpzlBt",
      source = "imageName"
  )
  @Mapping(
      target = "tpzlNr",
      source = "imageUrl"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjXhjhRxgrTwzl convert(PoliceProjectMaterial source, @MappingTarget VWjXhjhRxgrTwzl target);
}
