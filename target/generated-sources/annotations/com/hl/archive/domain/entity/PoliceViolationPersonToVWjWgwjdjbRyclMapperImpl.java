package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjWgwjdjbRycl;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T09:25:35+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceViolationPersonToVWjWgwjdjbRyclMapperImpl implements PoliceViolationPersonToVWjWgwjdjbRyclMapper {

    @Override
    public VWjWgwjdjbRycl convert(PoliceViolationPerson source) {
        if ( source == null ) {
            return null;
        }

        VWjWgwjdjbRycl vWjWgwjdjbRycl = new VWjWgwjdjbRycl();

        if ( source.getBirthDate() != null ) {
            vWjWgwjdjbRycl.setCsrq( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getBirthDate() ) );
        }
        vWjWgwjdjbRycl.setClxtmc( source.getDispositionCategory() );
        if ( source.getJoinPartyDate() != null ) {
            vWjWgwjdjbRycl.setRdrq( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getJoinPartyDate() ) );
        }
        vWjWgwjdjbRycl.setDwmc( source.getCaseOrg() );
        vWjWgwjdjbRycl.setZwmc( source.getPosition() );
        vWjWgwjdjbRycl.setWtXxzjbh( source.getWtXxzjbh() );
        vWjWgwjdjbRycl.setXxzjbh( source.getXxzjbh() );
        vWjWgwjdjbRycl.setZzmmmc( source.getPoliticalStatus() );
        vWjWgwjdjbRycl.setXm( source.getName() );
        vWjWgwjdjbRycl.setJzbmmc( source.getPoliceDept() );
        vWjWgwjdjbRycl.setBz( source.getRemark() );
        vWjWgwjdjbRycl.setXbmc( source.getGender() );
        vWjWgwjdjbRycl.setSfdcwzmc( source.getIsAccountability() );
        vWjWgwjdjbRycl.setZjmc( source.getRank() );

        return vWjWgwjdjbRycl;
    }

    @Override
    public VWjWgwjdjbRycl convert(PoliceViolationPerson source, VWjWgwjdjbRycl target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getBirthDate() != null ) {
            target.setCsrq( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getBirthDate() ) );
        }
        else {
            target.setCsrq( null );
        }
        target.setClxtmc( source.getDispositionCategory() );
        if ( source.getJoinPartyDate() != null ) {
            target.setRdrq( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getJoinPartyDate() ) );
        }
        else {
            target.setRdrq( null );
        }
        target.setDwmc( source.getCaseOrg() );
        target.setZwmc( source.getPosition() );
        target.setWtXxzjbh( source.getWtXxzjbh() );
        target.setXxzjbh( source.getXxzjbh() );
        target.setZzmmmc( source.getPoliticalStatus() );
        target.setXm( source.getName() );
        target.setJzbmmc( source.getPoliceDept() );
        target.setBz( source.getRemark() );
        target.setXbmc( source.getGender() );
        target.setSfdcwzmc( source.getIsAccountability() );
        target.setZjmc( source.getRank() );

        return target;
    }
}
