package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjyxl;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:08:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceTrainingToVWjRyjyxlMapperImpl implements PoliceTrainingToVWjRyjyxlMapper {

    @Override
    public VWjRyjyxl convert(PoliceTraining source) {
        if ( source == null ) {
            return null;
        }

        VWjRyjyxl vWjRyjyxl = new VWjRyjyxl();

        vWjRyjyxl.setPxbmc( source.getTrainingName() );
        vWjRyjyxl.setGmsfhm( source.getIdCard() );
        if ( source.getTrainingStartDate() != null ) {
            vWjRyjyxl.setPxqzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTrainingStartDate() ) );
        }
        vWjRyjyxl.setPxzbdwmc( source.getOrganizerName() );
        if ( source.getTrainingEndDate() != null ) {
            vWjRyjyxl.setPxzzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTrainingEndDate() ) );
        }

        return vWjRyjyxl;
    }

    @Override
    public VWjRyjyxl convert(PoliceTraining source, VWjRyjyxl target) {
        if ( source == null ) {
            return target;
        }

        target.setPxbmc( source.getTrainingName() );
        target.setGmsfhm( source.getIdCard() );
        if ( source.getTrainingStartDate() != null ) {
            target.setPxqzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTrainingStartDate() ) );
        }
        else {
            target.setPxqzsj( null );
        }
        target.setPxzbdwmc( source.getOrganizerName() );
        if ( source.getTrainingEndDate() != null ) {
            target.setPxzzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTrainingEndDate() ) );
        }
        else {
            target.setPxzzsj( null );
        }

        return target;
    }
}
