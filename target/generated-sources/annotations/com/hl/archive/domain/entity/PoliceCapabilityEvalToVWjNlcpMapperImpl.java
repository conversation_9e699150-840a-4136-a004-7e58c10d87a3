package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjNlcp;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:08:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceCapabilityEvalToVWjNlcpMapperImpl implements PoliceCapabilityEvalToVWjNlcpMapper {

    @Override
    public VWjNlcp convert(PoliceCapabilityEval source) {
        if ( source == null ) {
            return null;
        }

        VWjNlcp vWjNlcp = new VWjNlcp();

        vWjNlcp.setSqrxm( source.getParticipantName() );
        vWjNlcp.setShjgmc( source.getReviewResult() );
        vWjNlcp.setLcid( source.getLcid() );
        vWjNlcp.setDlmc( source.getCategoryName() );
        vWjNlcp.setDwmc( source.getOrgName() );
        vWjNlcp.setShrxm( source.getReviewer() );
        vWjNlcp.setFszt( source.getEvalStatus() );
        vWjNlcp.setZwmc( source.getPosition() );
        vWjNlcp.setBqzMc( source.getEvalLevel() );
        vWjNlcp.setBqMc( source.getFeatureName() );
        vWjNlcp.setFamc( source.getPlanName() );
        vWjNlcp.setJh( source.getPoliceNumber() );
        vWjNlcp.setXxzjbh( source.getXxzjbh() );

        return vWjNlcp;
    }

    @Override
    public VWjNlcp convert(PoliceCapabilityEval source, VWjNlcp target) {
        if ( source == null ) {
            return target;
        }

        target.setSqrxm( source.getParticipantName() );
        target.setShjgmc( source.getReviewResult() );
        target.setLcid( source.getLcid() );
        target.setDlmc( source.getCategoryName() );
        target.setDwmc( source.getOrgName() );
        target.setShrxm( source.getReviewer() );
        target.setFszt( source.getEvalStatus() );
        target.setZwmc( source.getPosition() );
        target.setBqzMc( source.getEvalLevel() );
        target.setBqMc( source.getFeatureName() );
        target.setFamc( source.getPlanName() );
        target.setJh( source.getPoliceNumber() );
        target.setXxzjbh( source.getXxzjbh() );

        return target;
    }
}
