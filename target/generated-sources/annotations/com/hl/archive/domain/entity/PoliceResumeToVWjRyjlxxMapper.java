package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjlxx;
import com.hl.orasync.domain.VWjRyjlxxToPoliceResumeMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {ConversionUtils.class,VWjRyjlxxToPoliceResumeMapper.class},
    imports = {}
)
public interface PoliceResumeToVWjRyjlxxMapper extends BaseMapper<PoliceResume, VWjRyjlxx> {
  @Mapping(
      target = "jzsj",
      source = "endDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "qssj",
      source = "startDate"
  )
  @Mapping(
      target = "szdw",
      source = "workUnit"
  )
  @Mapping(
      target = "zw",
      source = "position"
  )
  VWjRyjlxx convert(PoliceResume source);

  @Mapping(
      target = "jzsj",
      source = "endDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "qssj",
      source = "startDate"
  )
  @Mapping(
      target = "szdw",
      source = "workUnit"
  )
  @Mapping(
      target = "zw",
      source = "position"
  )
  VWjRyjlxx convert(PoliceResume source, @MappingTarget VWjRyjlxx target);
}
