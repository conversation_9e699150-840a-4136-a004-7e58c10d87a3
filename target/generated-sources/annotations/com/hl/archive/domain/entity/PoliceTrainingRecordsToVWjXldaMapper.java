package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXlda;
import com.hl.orasync.domain.VWjXldaToPoliceTrainingRecordsMapper;
import io.github.linpeilie.AutoMapperConfig__430;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__430.class,
    uses = {VWjXldaToPoliceTrainingRecordsMapper.class},
    imports = {}
)
public interface PoliceTrainingRecordsToVWjXldaMapper extends BaseMapper<PoliceTrainingRecords, VWjXlda> {
  @Mapping(
      target = "pxbmc",
      source = "trainingName"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "cj",
      source = "grade"
  )
  @Mapping(
      target = "kpxmmc",
      source = "examProjectName"
  )
  @Mapping(
      target = "pxsj",
      source = "trainingTime"
  )
  @Mapping(
      target = "pfdf",
      source = "score"
  )
  VWjXlda convert(PoliceTrainingRecords source);

  @Mapping(
      target = "pxbmc",
      source = "trainingName"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "cj",
      source = "grade"
  )
  @Mapping(
      target = "kpxmmc",
      source = "examProjectName"
  )
  @Mapping(
      target = "pxsj",
      source = "trainingTime"
  )
  @Mapping(
      target = "pfdf",
      source = "score"
  )
  VWjXlda convert(PoliceTrainingRecords source, @MappingTarget VWjXlda target);
}
