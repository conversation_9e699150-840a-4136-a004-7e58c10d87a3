package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjWgwjdjbRycl;
import com.hl.orasync.domain.VWjWgwjdjbRyclToPoliceViolationPersonMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {ConversionUtils.class,VWjWgwjdjbRyclToPoliceViolationPersonMapper.class},
    imports = {}
)
public interface PoliceViolationPersonToVWjWgwjdjbRyclMapper extends BaseMapper<PoliceViolationPerson, VWjWgwjdjbRycl> {
  @Mapping(
      target = "csrq",
      source = "birthDate"
  )
  @Mapping(
      target = "clxtmc",
      source = "dispositionCategory"
  )
  @Mapping(
      target = "rdrq",
      source = "joinPartyDate"
  )
  @Mapping(
      target = "dwmc",
      source = "caseOrg"
  )
  @Mapping(
      target = "zwmc",
      source = "position"
  )
  @Mapping(
      target = "wtXxzjbh",
      source = "wtXxzjbh"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "zzmmmc",
      source = "politicalStatus"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "jzbmmc",
      source = "policeDept"
  )
  @Mapping(
      target = "bz",
      source = "remark"
  )
  @Mapping(
      target = "xbmc",
      source = "gender"
  )
  @Mapping(
      target = "sfdcwzmc",
      source = "isAccountability"
  )
  @Mapping(
      target = "zjmc",
      source = "rank"
  )
  VWjWgwjdjbRycl convert(PoliceViolationPerson source);

  @Mapping(
      target = "csrq",
      source = "birthDate"
  )
  @Mapping(
      target = "clxtmc",
      source = "dispositionCategory"
  )
  @Mapping(
      target = "rdrq",
      source = "joinPartyDate"
  )
  @Mapping(
      target = "dwmc",
      source = "caseOrg"
  )
  @Mapping(
      target = "zwmc",
      source = "position"
  )
  @Mapping(
      target = "wtXxzjbh",
      source = "wtXxzjbh"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "zzmmmc",
      source = "politicalStatus"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "jzbmmc",
      source = "policeDept"
  )
  @Mapping(
      target = "bz",
      source = "remark"
  )
  @Mapping(
      target = "xbmc",
      source = "gender"
  )
  @Mapping(
      target = "sfdcwzmc",
      source = "isAccountability"
  )
  @Mapping(
      target = "zjmc",
      source = "rank"
  )
  VWjWgwjdjbRycl convert(PoliceViolationPerson source, @MappingTarget VWjWgwjdjbRycl target);
}
