package com.hl.orasync.convert;

import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.orasync.domain.VWjRyjbxx;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T14:14:00+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
public class PoliceBasicInfoConvertImpl implements PoliceBasicInfoConvert {

    @Override
    public PoliceBasicInfo convertPoliceBasicInfo(VWjRyjbxx vWjRyjbxx) {
        if ( vWjRyjbxx == null ) {
            return null;
        }

        PoliceBasicInfo policeBasicInfo = new PoliceBasicInfo();

        policeBasicInfo.setImgUrl( vWjRyjbxx.getZp() );
        policeBasicInfo.setName( vWjRyjbxx.getXm() );
        policeBasicInfo.setIdCard( vWjRyjbxx.getGmsfhm() );
        if ( vWjRyjbxx.getXb() != null ) {
            policeBasicInfo.setGender( ConversionUtils.mapGender( vWjRyjbxx.getXb() ).byteValue() );
        }
        policeBasicInfo.setBirthDate( ConversionUtils.strToLocalDate( vWjRyjbxx.getCsrq() ) );
        policeBasicInfo.setNativePlace( vWjRyjbxx.getJg() );
        policeBasicInfo.setNation( vWjRyjbxx.getMz() );
        policeBasicInfo.setPoliceNumber( vWjRyjbxx.getJh() );
        policeBasicInfo.setUnitName( vWjRyjbxx.getDwmc() );
        policeBasicInfo.setDepartment( vWjRyjbxx.getBm() );
        policeBasicInfo.setLeadershipLevel( vWjRyjbxx.getLdzwcj() );
        policeBasicInfo.setPoliceWorkStartDate( ConversionUtils.strToLocalDate( vWjRyjbxx.getCjgagzrq() ) );
        policeBasicInfo.setWorkStartDate( ConversionUtils.strToDate( vWjRyjbxx.getCjgzrq() ) );
        policeBasicInfo.setBloodType( vWjRyjbxx.getXx() );
        policeBasicInfo.setEntryChannel( vWjRyjbxx.getRjqd() );

        return policeBasicInfo;
    }
}
