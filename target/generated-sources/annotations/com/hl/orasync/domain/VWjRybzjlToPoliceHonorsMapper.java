package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHonors;
import com.hl.archive.domain.entity.PoliceHonorsToVWjRybzjlMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__430;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__430.class,
    uses = {ConversionUtils.class,PoliceHonorsToVWjRybzjlMapper.class},
    imports = {}
)
public interface VWjRybzjlToPoliceHonorsMapper extends BaseMapper<VWjRybzjl, PoliceHonors> {
  @Mapping(
      target = "approveAuthority",
      source = "jcpjjgmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "bh",
      source = "bh"
  )
  @Mapping(
      target = "honorName",
      source = "jcmc"
  )
  @Mapping(
      target = "awardDate",
      source = "jcsj",
      qualifiedByName = {"strToLocalDate"}
  )
  PoliceHonors convert(VWjRybzjl source);

  @Mapping(
      target = "approveAuthority",
      source = "jcpjjgmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "bh",
      source = "bh"
  )
  @Mapping(
      target = "honorName",
      source = "jcmc"
  )
  @Mapping(
      target = "awardDate",
      source = "jcsj",
      qualifiedByName = {"strToLocalDate"}
  )
  PoliceHonors convert(VWjRybzjl source, @MappingTarget PoliceHonors target);
}
