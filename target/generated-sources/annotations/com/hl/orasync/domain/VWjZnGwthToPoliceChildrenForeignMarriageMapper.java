package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceChildrenForeignMarriage;
import com.hl.archive.domain.entity.PoliceChildrenForeignMarriageToVWjZnGwthMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {ConversionUtils.class,PoliceChildrenForeignMarriageToVWjZnGwthMapper.class},
    imports = {}
)
public interface VWjZnGwthToPoliceChildrenForeignMarriageMapper extends BaseMapper<VWjZnGwth, PoliceChildrenForeignMarriage> {
  @Mapping(
      target = "spouseCountry",
      source = "gjZnpo"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "childName",
      source = "xmZn"
  )
  @Mapping(
      target = "registrationDate",
      source = "djrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "position",
      source = "zwZnpo"
  )
  @Mapping(
      target = "spouseName",
      source = "xmZnpo"
  )
  @Mapping(
      target = "workStudyUnit",
      source = "gzdwZnpo"
  )
  PoliceChildrenForeignMarriage convert(VWjZnGwth source);

  @Mapping(
      target = "spouseCountry",
      source = "gjZnpo"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "childName",
      source = "xmZn"
  )
  @Mapping(
      target = "registrationDate",
      source = "djrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "position",
      source = "zwZnpo"
  )
  @Mapping(
      target = "spouseName",
      source = "xmZnpo"
  )
  @Mapping(
      target = "workStudyUnit",
      source = "gzdwZnpo"
  )
  PoliceChildrenForeignMarriage convert(VWjZnGwth source,
      @MappingTarget PoliceChildrenForeignMarriage target);
}
