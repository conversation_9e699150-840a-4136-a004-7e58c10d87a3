package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceOnlineExam;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:08:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjWsksToPoliceOnlineExamMapperImpl implements VWjWsksToPoliceOnlineExamMapper {

    @Override
    public PoliceOnlineExam convert(VWjWsks source) {
        if ( source == null ) {
            return null;
        }

        PoliceOnlineExam policeOnlineExam = new PoliceOnlineExam();

        policeOnlineExam.setQuestionCount( ConversionUtils.bigDecimalToInteger( source.getTmgs() ) );
        policeOnlineExam.setScore( source.getDf() );
        policeOnlineExam.setIdCard( source.getGmsfhm() );
        policeOnlineExam.setStartTime( ConversionUtils.strToDate( source.getKssj() ) );
        policeOnlineExam.setEndTime( ConversionUtils.strToDate( source.getJssj() ) );
        policeOnlineExam.setExamDuration( ConversionUtils.bigDecimalToInteger( source.getKssc() ) );
        policeOnlineExam.setSubmitStatus( source.getSfjj() );
        policeOnlineExam.setExamPaperName( source.getSjmc() );

        return policeOnlineExam;
    }

    @Override
    public PoliceOnlineExam convert(VWjWsks source, PoliceOnlineExam target) {
        if ( source == null ) {
            return target;
        }

        target.setQuestionCount( ConversionUtils.bigDecimalToInteger( source.getTmgs() ) );
        target.setScore( source.getDf() );
        target.setIdCard( source.getGmsfhm() );
        target.setStartTime( ConversionUtils.strToDate( source.getKssj() ) );
        target.setEndTime( ConversionUtils.strToDate( source.getJssj() ) );
        target.setExamDuration( ConversionUtils.bigDecimalToInteger( source.getKssc() ) );
        target.setSubmitStatus( source.getSfjj() );
        target.setExamPaperName( source.getSjmc() );

        return target;
    }
}
