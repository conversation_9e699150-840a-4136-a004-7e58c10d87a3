package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHealthStatus;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:08:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrJkzkToPoliceHealthStatusMapperImpl implements VWjBrJkzkToPoliceHealthStatusMapper {

    @Override
    public PoliceHealthStatus convert(VWjBrJkzk source) {
        if ( source == null ) {
            return null;
        }

        PoliceHealthStatus policeHealthStatus = new PoliceHealthStatus();

        policeHealthStatus.setIdCard( source.getGmsfhm() );
        policeHealthStatus.setDiagnosisDate( ConversionUtils.strToDate( source.getZysj() ) );
        policeHealthStatus.setIllnessName( source.getJbmc() );

        return policeHealthStatus;
    }

    @Override
    public PoliceHealthStatus convert(VWjBrJkzk source, PoliceHealthStatus target) {
        if ( source == null ) {
            return target;
        }

        target.setIdCard( source.getGmsfhm() );
        target.setDiagnosisDate( ConversionUtils.strToDate( source.getZysj() ) );
        target.setIllnessName( source.getJbmc() );

        return target;
    }
}
