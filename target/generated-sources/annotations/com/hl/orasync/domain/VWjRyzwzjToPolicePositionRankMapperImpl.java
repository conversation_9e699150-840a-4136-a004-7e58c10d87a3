package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PolicePositionRank;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T09:25:35+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyzwzjToPolicePositionRankMapperImpl implements VWjRyzwzjToPolicePositionRankMapper {

    @Override
    public PolicePositionRank convert(VWjRyzwzj source) {
        if ( source == null ) {
            return null;
        }

        PolicePositionRank policePositionRank = new PolicePositionRank();

        policePositionRank.setPolicePositionLevel( source.getGazwjb() );
        policePositionRank.setPositionName( source.getZwmc() );
        policePositionRank.setCurrentPositionDate( ConversionUtils.strToDate( source.getZwsxsj() ) );
        policePositionRank.setIdCard( source.getGmsfhm() );
        policePositionRank.setAppointmentDocument( source.getRzwh() );
        policePositionRank.setCurrentRankDate( ConversionUtils.strToDate( source.getXzjsj() ) );

        return policePositionRank;
    }

    @Override
    public PolicePositionRank convert(VWjRyzwzj source, PolicePositionRank target) {
        if ( source == null ) {
            return target;
        }

        target.setPolicePositionLevel( source.getGazwjb() );
        target.setPositionName( source.getZwmc() );
        target.setCurrentPositionDate( ConversionUtils.strToDate( source.getZwsxsj() ) );
        target.setIdCard( source.getGmsfhm() );
        target.setAppointmentDocument( source.getRzwh() );
        target.setCurrentRankDate( ConversionUtils.strToDate( source.getXzjsj() ) );

        return target;
    }
}
