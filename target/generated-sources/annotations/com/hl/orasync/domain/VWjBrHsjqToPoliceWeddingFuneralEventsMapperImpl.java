package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceWeddingFuneralEvents;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T09:25:35+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrHsjqToPoliceWeddingFuneralEventsMapperImpl implements VWjBrHsjqToPoliceWeddingFuneralEventsMapper {

    @Override
    public PoliceWeddingFuneralEvents convert(VWjBrHsjq source) {
        if ( source == null ) {
            return null;
        }

        PoliceWeddingFuneralEvents policeWeddingFuneralEvents = new PoliceWeddingFuneralEvents();

        policeWeddingFuneralEvents.setExpenseAmount( ConversionUtils.strToBigDecimal( source.getJe() ) );
        policeWeddingFuneralEvents.setParticipantCount( ConversionUtils.bigDecimalToInteger( source.getCyrs() ) );
        policeWeddingFuneralEvents.setRelationshipWithParty( source.getGxDsr() );
        policeWeddingFuneralEvents.setIdCard( source.getGmsfhm() );
        policeWeddingFuneralEvents.setPartyName( source.getXmDsr() );
        policeWeddingFuneralEvents.setEventType( source.getCblxmc() );
        policeWeddingFuneralEvents.setEventDate( ConversionUtils.strToDate( source.getCbrq() ) );

        return policeWeddingFuneralEvents;
    }

    @Override
    public PoliceWeddingFuneralEvents convert(VWjBrHsjq source, PoliceWeddingFuneralEvents target) {
        if ( source == null ) {
            return target;
        }

        target.setExpenseAmount( ConversionUtils.strToBigDecimal( source.getJe() ) );
        target.setParticipantCount( ConversionUtils.bigDecimalToInteger( source.getCyrs() ) );
        target.setRelationshipWithParty( source.getGxDsr() );
        target.setIdCard( source.getGmsfhm() );
        target.setPartyName( source.getXmDsr() );
        target.setEventType( source.getCblxmc() );
        target.setEventDate( ConversionUtils.strToDate( source.getCbrq() ) );

        return target;
    }
}
