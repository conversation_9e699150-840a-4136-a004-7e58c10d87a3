package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyRealEstate;
import com.hl.archive.domain.entity.PoliceFamilyRealEstateToVWjQtFcqkMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__430;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__430.class,
    uses = {ConversionUtils.class,PoliceFamilyRealEstateToVWjQtFcqkMapper.class},
    imports = {}
)
public interface VWjQtFcqkToPoliceFamilyRealEstateMapper extends BaseMapper<VWjQtFcqk, PoliceFamilyRealEstate> {
  @Mapping(
      target = "propertyDisposition",
      source = "fcqxmc"
  )
  @Mapping(
      target = "salePrice",
      source = "csjg"
  )
  @Mapping(
      target = "propertyAddress",
      source = "dz"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "propertyType",
      source = "fclxmc"
  )
  @Mapping(
      target = "propertySource",
      source = "fclymc"
  )
  @Mapping(
      target = "transactionPrice",
      source = "jyjg"
  )
  @Mapping(
      target = "buildingArea",
      source = "jzmj"
  )
  @Mapping(
      target = "saleDate",
      source = "cssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "propertyOwnerName",
      source = "xmCqr"
  )
  @Mapping(
      target = "transactionDate",
      source = "jysj",
      qualifiedByName = {"strToDate"}
  )
  PoliceFamilyRealEstate convert(VWjQtFcqk source);

  @Mapping(
      target = "propertyDisposition",
      source = "fcqxmc"
  )
  @Mapping(
      target = "salePrice",
      source = "csjg"
  )
  @Mapping(
      target = "propertyAddress",
      source = "dz"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "propertyType",
      source = "fclxmc"
  )
  @Mapping(
      target = "propertySource",
      source = "fclymc"
  )
  @Mapping(
      target = "transactionPrice",
      source = "jyjg"
  )
  @Mapping(
      target = "buildingArea",
      source = "jzmj"
  )
  @Mapping(
      target = "saleDate",
      source = "cssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "propertyOwnerName",
      source = "xmCqr"
  )
  @Mapping(
      target = "transactionDate",
      source = "jysj",
      qualifiedByName = {"strToDate"}
  )
  PoliceFamilyRealEstate convert(VWjQtFcqk source, @MappingTarget PoliceFamilyRealEstate target);
}
