package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHkMacauTaiwanTravel;
import com.hl.archive.domain.entity.PoliceHkMacauTaiwanTravelToVWjBrGatwlqkMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__430;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__430.class,
    uses = {ConversionUtils.class,PoliceHkMacauTaiwanTravelToVWjBrGatwlqkMapper.class},
    imports = {}
)
public interface VWjBrGatwlqkToPoliceHkMacauTaiwanTravelMapper extends BaseMapper<VWjBrGatwlqk, PoliceHkMacauTaiwanTravel> {
  @Mapping(
      target = "destinationRegion",
      source = "sdgj"
  )
  @Mapping(
      target = "endDate",
      source = "jssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "documentNumber",
      source = "zjhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "travelReason",
      source = "sy"
  )
  @Mapping(
      target = "approvalAuthority",
      source = "spjgmc"
  )
  @Mapping(
      target = "startDate",
      source = "kssj",
      qualifiedByName = {"strToDate"}
  )
  PoliceHkMacauTaiwanTravel convert(VWjBrGatwlqk source);

  @Mapping(
      target = "destinationRegion",
      source = "sdgj"
  )
  @Mapping(
      target = "endDate",
      source = "jssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "documentNumber",
      source = "zjhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "travelReason",
      source = "sy"
  )
  @Mapping(
      target = "approvalAuthority",
      source = "spjgmc"
  )
  @Mapping(
      target = "startDate",
      source = "kssj",
      qualifiedByName = {"strToDate"}
  )
  PoliceHkMacauTaiwanTravel convert(VWjBrGatwlqk source,
      @MappingTarget PoliceHkMacauTaiwanTravel target);
}
