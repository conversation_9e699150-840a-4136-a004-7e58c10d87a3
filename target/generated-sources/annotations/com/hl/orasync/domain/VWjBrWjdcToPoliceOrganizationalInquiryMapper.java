package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceOrganizationalInquiry;
import com.hl.archive.domain.entity.PoliceOrganizationalInquiryToVWjBrWjdcMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {ConversionUtils.class,PoliceOrganizationalInquiryToVWjBrWjdcMapper.class},
    imports = {}
)
public interface VWjBrWjdcToPoliceOrganizationalInquiryMapper extends BaseMapper<VWjBrWjdc, PoliceOrganizationalInquiry> {
  @Mapping(
      target = "handlingAuthority",
      source = "dcjg"
  )
  @Mapping(
      target = "suspectedViolations",
      source = "wfqk"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "handlingDate",
      source = "dcrq",
      qualifiedByName = {"strToDate"}
  )
  PoliceOrganizationalInquiry convert(VWjBrWjdc source);

  @Mapping(
      target = "handlingAuthority",
      source = "dcjg"
  )
  @Mapping(
      target = "suspectedViolations",
      source = "wfqk"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "handlingDate",
      source = "dcrq",
      qualifiedByName = {"strToDate"}
  )
  PoliceOrganizationalInquiry convert(VWjBrWjdc source,
      @MappingTarget PoliceOrganizationalInquiry target);
}
