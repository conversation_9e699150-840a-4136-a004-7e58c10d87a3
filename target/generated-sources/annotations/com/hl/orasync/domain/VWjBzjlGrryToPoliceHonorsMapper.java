package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHonors;
import com.hl.archive.domain.entity.PoliceHonorsToVWjBzjlGrryMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {ConversionUtils.class,PoliceHonorsToVWjBzjlGrryMapper.class},
    imports = {}
)
public interface VWjBzjlGrryToPoliceHonorsMapper extends BaseMapper<VWjBzjlGrry, PoliceHonors> {
  @Mapping(
      target = "approveAuthority",
      source = "jljgmc"
  )
  @Mapping(
      target = "honorLevel",
      source = "ryjbmc"
  )
  @Mapping(
      target = "organizationName",
      source = "dwmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "bh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "honorName",
      source = "jlmc"
  )
  @Mapping(
      target = "awardDate",
      source = "bzsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "awardDocNo",
      source = "bzwh"
  )
  PoliceHonors convert(VWjBzjlGrry source);

  @Mapping(
      target = "approveAuthority",
      source = "jljgmc"
  )
  @Mapping(
      target = "honorLevel",
      source = "ryjbmc"
  )
  @Mapping(
      target = "organizationName",
      source = "dwmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "bh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "honorName",
      source = "jlmc"
  )
  @Mapping(
      target = "awardDate",
      source = "bzsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "awardDocNo",
      source = "bzwh"
  )
  PoliceHonors convert(VWjBzjlGrry source, @MappingTarget PoliceHonors target);
}
