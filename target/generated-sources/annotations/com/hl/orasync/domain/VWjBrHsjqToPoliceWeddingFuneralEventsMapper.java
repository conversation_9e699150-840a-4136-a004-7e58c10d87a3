package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceWeddingFuneralEvents;
import com.hl.archive.domain.entity.PoliceWeddingFuneralEventsToVWjBrHsjqMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__430;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__430.class,
    uses = {ConversionUtils.class,PoliceWeddingFuneralEventsToVWjBrHsjqMapper.class},
    imports = {}
)
public interface VWjBrHsjqToPoliceWeddingFuneralEventsMapper extends BaseMapper<VWjBrHsjq, PoliceWeddingFuneralEvents> {
  @Mapping(
      target = "expenseAmount",
      source = "je",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "participantCount",
      source = "cyrs",
      qualifiedByName = {"bigDecimalToInteger"}
  )
  @Mapping(
      target = "relationshipWithParty",
      source = "gxDsr"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "partyName",
      source = "xmDsr"
  )
  @Mapping(
      target = "eventType",
      source = "cblxmc"
  )
  @Mapping(
      target = "eventDate",
      source = "cbrq",
      qualifiedByName = {"strToDate"}
  )
  PoliceWeddingFuneralEvents convert(VWjBrHsjq source);

  @Mapping(
      target = "expenseAmount",
      source = "je",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "participantCount",
      source = "cyrs",
      qualifiedByName = {"bigDecimalToInteger"}
  )
  @Mapping(
      target = "relationshipWithParty",
      source = "gxDsr"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "partyName",
      source = "xmDsr"
  )
  @Mapping(
      target = "eventType",
      source = "cblxmc"
  )
  @Mapping(
      target = "eventDate",
      source = "cbrq",
      qualifiedByName = {"strToDate"}
  )
  PoliceWeddingFuneralEvents convert(VWjBrHsjq source,
      @MappingTarget PoliceWeddingFuneralEvents target);
}
