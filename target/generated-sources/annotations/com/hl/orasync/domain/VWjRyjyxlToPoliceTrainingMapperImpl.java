package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceTraining;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T09:25:35+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyjyxlToPoliceTrainingMapperImpl implements VWjRyjyxlToPoliceTrainingMapper {

    @Override
    public PoliceTraining convert(VWjRyjyxl source) {
        if ( source == null ) {
            return null;
        }

        PoliceTraining policeTraining = new PoliceTraining();

        policeTraining.setTrainingEndDate( ConversionUtils.strToDate( source.getPxzzsj() ) );
        policeTraining.setTrainingStartDate( ConversionUtils.strToDate( source.getPxqzsj() ) );
        policeTraining.setTrainingName( source.getPxbmc() );
        policeTraining.setIdCard( source.getGmsfhm() );
        policeTraining.setOrganizerName( source.getPxzbdwmc() );

        return policeTraining;
    }

    @Override
    public PoliceTraining convert(VWjRyjyxl source, PoliceTraining target) {
        if ( source == null ) {
            return target;
        }

        target.setTrainingEndDate( ConversionUtils.strToDate( source.getPxzzsj() ) );
        target.setTrainingStartDate( ConversionUtils.strToDate( source.getPxqzsj() ) );
        target.setTrainingName( source.getPxbmc() );
        target.setIdCard( source.getGmsfhm() );
        target.setOrganizerName( source.getPxzbdwmc() );

        return target;
    }
}
