package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyMembers;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:08:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyjtcyToPoliceFamilyMembersMapperImpl implements VWjRyjtcyToPoliceFamilyMembersMapper {

    @Override
    public PoliceFamilyMembers convert(VWjRyjtcy source) {
        if ( source == null ) {
            return null;
        }

        PoliceFamilyMembers policeFamilyMembers = new PoliceFamilyMembers();

        policeFamilyMembers.setMobilePhone( source.getSjhm() );
        policeFamilyMembers.setIdCard( source.getGmsfhm() );
        policeFamilyMembers.setPoliticalStatus( source.getZzmm() );
        policeFamilyMembers.setWorkUnitPosition( source.getGzdw() );
        policeFamilyMembers.setMemberName( source.getXm() );
        policeFamilyMembers.setRelationship( source.getRygx() );
        policeFamilyMembers.setBirthDate( source.getCsrq() );

        return policeFamilyMembers;
    }

    @Override
    public PoliceFamilyMembers convert(VWjRyjtcy source, PoliceFamilyMembers target) {
        if ( source == null ) {
            return target;
        }

        target.setMobilePhone( source.getSjhm() );
        target.setIdCard( source.getGmsfhm() );
        target.setPoliticalStatus( source.getZzmm() );
        target.setWorkUnitPosition( source.getGzdw() );
        target.setMemberName( source.getXm() );
        target.setRelationship( source.getRygx() );
        target.setBirthDate( source.getCsrq() );

        return target;
    }
}
