package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceContactInfo;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T09:25:34+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyjtzzToPoliceContactInfoMapperImpl implements VWjRyjtzzToPoliceContactInfoMapper {

    @Override
    public PoliceContactInfo convert(VWjRyjtzz source) {
        if ( source == null ) {
            return null;
        }

        PoliceContactInfo policeContactInfo = new PoliceContactInfo();

        policeContactInfo.setMobilePhone( source.getShhm() );
        policeContactInfo.setIdCard( source.getGmsfhm() );
        policeContactInfo.setMobileShortNumber( source.getShhmdh() );
        policeContactInfo.setHomePhone( source.getJtdh() );
        policeContactInfo.setHomeAddress( source.getJtdz() );

        return policeContactInfo;
    }

    @Override
    public PoliceContactInfo convert(VWjRyjtzz source, PoliceContactInfo target) {
        if ( source == null ) {
            return target;
        }

        target.setMobilePhone( source.getShhm() );
        target.setIdCard( source.getGmsfhm() );
        target.setMobileShortNumber( source.getShhmdh() );
        target.setHomePhone( source.getJtdh() );
        target.setHomeAddress( source.getJtdz() );

        return target;
    }
}
