package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceUnitAward;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:08:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBzjlDwryToPoliceUnitAwardMapperImpl implements VWjBzjlDwryToPoliceUnitAwardMapper {

    @Override
    public PoliceUnitAward convert(VWjBzjlDwry source) {
        if ( source == null ) {
            return null;
        }

        PoliceUnitAward policeUnitAward = new PoliceUnitAward();

        policeUnitAward.setSupervisorName( source.getXm() );
        policeUnitAward.setUnit( source.getDwmc() );
        policeUnitAward.setDocumentNumber( source.getBzwh() );
        policeUnitAward.setSupervisorCode( source.getJh() );
        policeUnitAward.setAwardOrgan( source.getJljgmc() );
        policeUnitAward.setAwardName( source.getJlmc() );
        policeUnitAward.setAwardTime( ConversionUtils.strToDate( source.getBzsj() ) );
        policeUnitAward.setZjbh( source.getXxzjbh() );

        return policeUnitAward;
    }

    @Override
    public PoliceUnitAward convert(VWjBzjlDwry source, PoliceUnitAward target) {
        if ( source == null ) {
            return target;
        }

        target.setSupervisorName( source.getXm() );
        target.setUnit( source.getDwmc() );
        target.setDocumentNumber( source.getBzwh() );
        target.setSupervisorCode( source.getJh() );
        target.setAwardOrgan( source.getJljgmc() );
        target.setAwardName( source.getJlmc() );
        target.setAwardTime( ConversionUtils.strToDate( source.getBzsj() ) );
        target.setZjbh( source.getXxzjbh() );

        return target;
    }
}
