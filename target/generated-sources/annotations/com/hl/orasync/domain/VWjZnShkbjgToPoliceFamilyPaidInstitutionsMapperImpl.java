package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyPaidInstitutions;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:08:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjZnShkbjgToPoliceFamilyPaidInstitutionsMapperImpl implements VWjZnShkbjgToPoliceFamilyPaidInstitutionsMapper {

    @Override
    public PoliceFamilyPaidInstitutions convert(VWjZnShkbjg source) {
        if ( source == null ) {
            return null;
        }

        PoliceFamilyPaidInstitutions policeFamilyPaidInstitutions = new PoliceFamilyPaidInstitutions();

        policeFamilyPaidInstitutions.setRegisteredCapital( ConversionUtils.strToBigDecimal( source.getZczb() ) );
        policeFamilyPaidInstitutions.setEstablishmentDate( ConversionUtils.strToDate( source.getClsj() ) );
        policeFamilyPaidInstitutions.setIdCard( source.getGmsfhm() );
        policeFamilyPaidInstitutions.setInstitutionName( source.getQymc() );
        policeFamilyPaidInstitutions.setName( source.getXmFr() );
        policeFamilyPaidInstitutions.setBusinessScope( source.getJyfw() );
        policeFamilyPaidInstitutions.setInstitutionType( source.getQylxmc() );
        policeFamilyPaidInstitutions.setRegistrationAddress( source.getZcd() );
        policeFamilyPaidInstitutions.setBusinessAddress( source.getJyd() );
        policeFamilyPaidInstitutions.setSocialCreditCode( source.getZch() );

        return policeFamilyPaidInstitutions;
    }

    @Override
    public PoliceFamilyPaidInstitutions convert(VWjZnShkbjg source, PoliceFamilyPaidInstitutions target) {
        if ( source == null ) {
            return target;
        }

        target.setRegisteredCapital( ConversionUtils.strToBigDecimal( source.getZczb() ) );
        target.setEstablishmentDate( ConversionUtils.strToDate( source.getClsj() ) );
        target.setIdCard( source.getGmsfhm() );
        target.setInstitutionName( source.getQymc() );
        target.setName( source.getXmFr() );
        target.setBusinessScope( source.getJyfw() );
        target.setInstitutionType( source.getQylxmc() );
        target.setRegistrationAddress( source.getZcd() );
        target.setBusinessAddress( source.getJyd() );
        target.setSocialCreditCode( source.getZch() );

        return target;
    }
}
