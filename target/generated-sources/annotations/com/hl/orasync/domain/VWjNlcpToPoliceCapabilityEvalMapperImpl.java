package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceCapabilityEval;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:08:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjNlcpToPoliceCapabilityEvalMapperImpl implements VWjNlcpToPoliceCapabilityEvalMapper {

    @Override
    public PoliceCapabilityEval convert(VWjNlcp source) {
        if ( source == null ) {
            return null;
        }

        PoliceCapabilityEval policeCapabilityEval = new PoliceCapabilityEval();

        policeCapabilityEval.setLcid( source.getLcid() );
        policeCapabilityEval.setPoliceNumber( source.getJh() );
        policeCapabilityEval.setOrgName( source.getDwmc() );
        policeCapabilityEval.setEvalStatus( source.getFszt() );
        policeCapabilityEval.setReviewResult( source.getShjgmc() );
        policeCapabilityEval.setFeatureName( source.getBqMc() );
        policeCapabilityEval.setEvalLevel( source.getBqzMc() );
        policeCapabilityEval.setPlanName( source.getFamc() );
        policeCapabilityEval.setReviewer( source.getShrxm() );
        policeCapabilityEval.setPosition( source.getZwmc() );
        policeCapabilityEval.setParticipantName( source.getSqrxm() );
        policeCapabilityEval.setCategoryName( source.getDlmc() );
        policeCapabilityEval.setXxzjbh( source.getXxzjbh() );

        return policeCapabilityEval;
    }

    @Override
    public PoliceCapabilityEval convert(VWjNlcp source, PoliceCapabilityEval target) {
        if ( source == null ) {
            return target;
        }

        target.setLcid( source.getLcid() );
        target.setPoliceNumber( source.getJh() );
        target.setOrgName( source.getDwmc() );
        target.setEvalStatus( source.getFszt() );
        target.setReviewResult( source.getShjgmc() );
        target.setFeatureName( source.getBqMc() );
        target.setEvalLevel( source.getBqzMc() );
        target.setPlanName( source.getFamc() );
        target.setReviewer( source.getShrxm() );
        target.setPosition( source.getZwmc() );
        target.setParticipantName( source.getSqrxm() );
        target.setCategoryName( source.getDlmc() );
        target.setXxzjbh( source.getXxzjbh() );

        return target;
    }
}
