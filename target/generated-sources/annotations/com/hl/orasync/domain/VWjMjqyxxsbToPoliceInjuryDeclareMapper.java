package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceInjuryDeclare;
import com.hl.archive.domain.entity.PoliceInjuryDeclareToVWjMjqyxxsbMapper;
import io.github.linpeilie.AutoMapperConfig__430;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__430.class,
    uses = {PoliceInjuryDeclareToVWjMjqyxxsbMapper.class},
    imports = {}
)
public interface VWjMjqyxxsbToPoliceInjuryDeclareMapper extends BaseMapper<VWjMjqyxxsb, PoliceInjuryDeclare> {
  @Mapping(
      target = "policeNumber",
      source = "jh"
  )
  @Mapping(
      target = "orgName",
      source = "gzdwGajgmc"
  )
  @Mapping(
      target = "currentStatus",
      source = "hcrdztmc"
  )
  @Mapping(
      target = "declareType",
      source = "sqlbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "position",
      source = "zwmc"
  )
  @Mapping(
      target = "injuryEvent",
      source = "sqly"
  )
  PoliceInjuryDeclare convert(VWjMjqyxxsb source);

  @Mapping(
      target = "policeNumber",
      source = "jh"
  )
  @Mapping(
      target = "orgName",
      source = "gzdwGajgmc"
  )
  @Mapping(
      target = "currentStatus",
      source = "hcrdztmc"
  )
  @Mapping(
      target = "declareType",
      source = "sqlbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "position",
      source = "zwmc"
  )
  @Mapping(
      target = "injuryEvent",
      source = "sqly"
  )
  PoliceInjuryDeclare convert(VWjMjqyxxsb source, @MappingTarget PoliceInjuryDeclare target);
}
