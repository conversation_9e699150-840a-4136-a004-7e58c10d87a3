package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceProjectMaterial;
import com.hl.archive.domain.entity.PoliceProjectMaterialToVWjXhjhRxgrTwzlMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {ConversionUtils.class,PoliceProjectMaterialToVWjXhjhRxgrTwzlMapper.class},
    imports = {}
)
public interface VWjXhjhRxgrTwzlToPoliceProjectMaterialMapper extends BaseMapper<VWjXhjhRxgrTwzl, PoliceProjectMaterial> {
  @Mapping(
      target = "imageName",
      source = "tpzlBt"
  )
  @Mapping(
      target = "imageUrl",
      source = "tpzlNr"
  )
  @Mapping(
      target = "xhjhZjbh",
      source = "jlXxzjbh"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceProjectMaterial convert(VWjXhjhRxgrTwzl source);

  @Mapping(
      target = "imageName",
      source = "tpzlBt"
  )
  @Mapping(
      target = "imageUrl",
      source = "tpzlNr"
  )
  @Mapping(
      target = "xhjhZjbh",
      source = "jlXxzjbh"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceProjectMaterial convert(VWjXhjhRxgrTwzl source,
      @MappingTarget PoliceProjectMaterial target);
}
