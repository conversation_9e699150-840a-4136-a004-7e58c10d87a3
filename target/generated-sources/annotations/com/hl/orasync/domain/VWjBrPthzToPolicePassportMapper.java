package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PolicePassport;
import com.hl.archive.domain.entity.PolicePassportToVWjBrPthzMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__430;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__430.class,
    uses = {ConversionUtils.class,PolicePassportToVWjBrPthzMapper.class},
    imports = {}
)
public interface VWjBrPthzToPolicePassportMapper extends BaseMapper<VWjBrPthz, PolicePassport> {
  @Mapping(
      target = "expiryDate",
      source = "yxqz",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "passportNumber",
      source = "hzhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "issueDate",
      source = "qfrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "remarks",
      source = "bz"
  )
  @Mapping(
      target = "custodyOrganization",
      source = "bgjgmc"
  )
  PolicePassport convert(VWjBrPthz source);

  @Mapping(
      target = "expiryDate",
      source = "yxqz",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "passportNumber",
      source = "hzhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "issueDate",
      source = "qfrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "remarks",
      source = "bz"
  )
  @Mapping(
      target = "custodyOrganization",
      source = "bgjgmc"
  )
  PolicePassport convert(VWjBrPthz source, @MappingTarget PolicePassport target);
}
